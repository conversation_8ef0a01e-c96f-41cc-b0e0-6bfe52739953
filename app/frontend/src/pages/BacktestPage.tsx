import React, { useState, useEffect } from 'react';
import {
  Container,
  Typography,
  Box,
  Paper,
  Grid,
  TextField,
  Button,
  Alert,
  CircularProgress,
  Card,
  CardContent,
  Divider,
} from '@mui/material';
import { PlayArrow, Stop, Assessment } from '@mui/icons-material';
import StockSelector from '../components/StockSelector';
import AnalystSelector from '../components/AnalystSelector';
import ProviderSelector from '../components/ProviderSelector';
import ModelSelector from '../components/ModelSelector';
import {
  Stock,
  Analyst,
  LLMProvider,
  LLMModel,
  BacktestFormData,
  BacktestFormErrors,
  BacktestState
} from '../types';
import {
  getAvailableAnalysts,
  getAvailableProviders,
  getAvailableModels,
  checkOllamaStatus,
  checkLMStudioStatus,
  runBacktest
} from '../services/api';

const BacktestPage: React.FC = () => {
  const [formData, setFormData] = useState<BacktestFormData>({
    selectedStocks: [],
    selectedAnalysts: [],
    selectedProvider: '',
    selectedModel: '',
    startDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 30 days ago
    endDate: new Date().toISOString().split('T')[0],
    initialCapital: 100000,
    marginRequirement: 0,
  });

  const [formErrors, setFormErrors] = useState<BacktestFormErrors>({});
  const [analysts, setAnalysts] = useState<Analyst[]>([]);
  const [providers, setProviders] = useState<LLMProvider[]>([]);
  const [models, setModels] = useState<LLMModel[]>([]);
  const [backtestState, setBacktestState] = useState<BacktestState>({
    isRunning: false,
    progress: [],
    result: null,
    error: null,
  });

  // Load initial data
  useEffect(() => {
    const loadInitialData = async () => {
      try {
        const [analystsData, providersData] = await Promise.all([
          getAvailableAnalysts(),
          getAvailableProviders(),
        ]);

        setAnalysts(analystsData);
        setProviders(providersData);

        // Auto-select first provider if available
        if (providersData.length > 0 && !formData.selectedProvider) {
          setFormData(prev => ({ ...prev, selectedProvider: providersData[0].id }));
        }
      } catch (error) {
        console.error('Error loading initial data:', error);
      }
    };

    loadInitialData();
  }, []);

  // Load models when provider changes
  useEffect(() => {
    const loadModels = async () => {
      if (!formData.selectedProvider) {
        setModels([]);
        return;
      }

      try {
        // Check provider status for local providers
        if (formData.selectedProvider === 'Ollama') {
          const status = await checkOllamaStatus();
          if (!status.running) {
            console.warn('Ollama is not running');
          }
        } else if (formData.selectedProvider === 'LMStudio') {
          const status = await checkLMStudioStatus();
          if (!status.running) {
            console.warn('LM Studio is not running');
          }
        }

        const modelsData = await getAvailableModels(formData.selectedProvider);
        setModels(modelsData);

        // Auto-select first model if available and none selected
        if (modelsData.length > 0 && !formData.selectedModel) {
          setFormData(prev => ({ ...prev, selectedModel: modelsData[0].id }));
        } else if (modelsData.length === 0) {
          setFormData(prev => ({ ...prev, selectedModel: '' }));
        }
      } catch (error) {
        console.error('Error loading models:', error);
        setModels([]);
      }
    };

    loadModels();
  }, [formData.selectedProvider]);

  const validateForm = (): boolean => {
    const errors: BacktestFormErrors = {};

    if (formData.selectedStocks.length === 0) {
      errors.stocks = 'Please select at least one stock';
    }

    if (formData.selectedAnalysts.length === 0) {
      errors.analysts = 'Please select at least one analyst';
    }

    if (!formData.selectedProvider) {
      errors.provider = 'Please select an LLM provider';
    }

    if (!formData.selectedModel) {
      errors.model = 'Please select a model';
    }

    if (!formData.startDate) {
      errors.startDate = 'Please select a start date';
    }

    if (!formData.endDate) {
      errors.endDate = 'Please select an end date';
    }

    if (formData.startDate && formData.endDate && formData.startDate >= formData.endDate) {
      errors.endDate = 'End date must be after start date';
    }

    if (formData.initialCapital <= 0) {
      errors.initialCapital = 'Initial capital must be greater than 0';
    }

    if (formData.marginRequirement < 0 || formData.marginRequirement > 1) {
      errors.marginRequirement = 'Margin requirement must be between 0 and 1';
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleStartBacktest = async () => {
    if (!validateForm()) {
      return;
    }

    setBacktestState({
      isRunning: true,
      progress: [],
      result: null,
      error: null,
    });

    try {
      const cancelFunction = runBacktest(
        {
          tickers: formData.selectedStocks.map(stock => stock.ticker),
          selectedAnalysts: formData.selectedAnalysts,
          modelProvider: formData.selectedProvider,
          modelName: formData.selectedModel,
          startDate: formData.startDate,
          endDate: formData.endDate,
          initialCapital: formData.initialCapital,
          marginRequirement: formData.marginRequirement,
        },
        (progress) => {
          setBacktestState(prev => ({
            ...prev,
            progress: [...prev.progress, progress],
          }));
        },
        (result) => {
          setBacktestState(prev => ({
            ...prev,
            isRunning: false,
            result,
          }));
        },
        (error) => {
          setBacktestState(prev => ({
            ...prev,
            isRunning: false,
            error,
          }));
        }
      );

      setBacktestState(prev => ({
        ...prev,
        cancelFunction,
      }));
    } catch (error) {
      setBacktestState(prev => ({
        ...prev,
        isRunning: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred',
      }));
    }
  };

  const handleCancelBacktest = () => {
    if (backtestState.cancelFunction) {
      backtestState.cancelFunction();
    }
    setBacktestState(prev => ({
      ...prev,
      isRunning: false,
      cancelFunction: undefined,
    }));
  };

  return (
    <Container maxWidth="xl" sx={{ mt: 4, mb: 4 }}>
      <Grid container spacing={3}>
        {/* Configuration Panel */}
        <Grid size={{ xs: 12, md: 6 }}>
          <Paper elevation={3} sx={{ p: 3, height: 'fit-content' }}>
            <Typography variant="h5" gutterBottom>
              <Assessment sx={{ mr: 1, verticalAlign: 'middle' }} />
              Backtest Configuration
            </Typography>

            <Box sx={{ mt: 3 }}>
              <StockSelector
                selectedStocks={formData.selectedStocks}
                onChange={(stocks) => setFormData(prev => ({ ...prev, selectedStocks: stocks }))}
                error={formErrors.stocks}
                maxSelections={5}
              />
            </Box>

            <Box sx={{ mt: 3 }}>
              <AnalystSelector
                analysts={analysts}
                selectedAnalysts={formData.selectedAnalysts}
                onChange={(analysts) => setFormData(prev => ({ ...prev, selectedAnalysts: analysts }))}
                error={formErrors.analysts}
              />
            </Box>

            <Box sx={{ mt: 3 }}>
              <ProviderSelector
                providers={providers}
                selectedProvider={formData.selectedProvider}
                onChange={(provider) => setFormData(prev => ({ ...prev, selectedProvider: provider }))}
                error={formErrors.provider}
              />
            </Box>

            <Box sx={{ mt: 3 }}>
              <ModelSelector
                models={models}
                selectedModel={formData.selectedModel}
                onChange={(model) => setFormData(prev => ({ ...prev, selectedModel: model }))}
                error={formErrors.model}
                provider={formData.selectedProvider}
                disabled={!formData.selectedProvider || models.length === 0}
              />
            </Box>

            <Box sx={{ mt: 3 }}>
              <Grid container spacing={2}>
                <Grid size={{ xs: 6 }}>
                  <TextField
                    fullWidth
                    label="Start Date"
                    type="date"
                    value={formData.startDate}
                    onChange={(e) => setFormData(prev => ({ ...prev, startDate: e.target.value }))}
                    error={!!formErrors.startDate}
                    helperText={formErrors.startDate}
                    InputLabelProps={{ shrink: true }}
                  />
                </Grid>
                <Grid size={{ xs: 6 }}>
                  <TextField
                    fullWidth
                    label="End Date"
                    type="date"
                    value={formData.endDate}
                    onChange={(e) => setFormData(prev => ({ ...prev, endDate: e.target.value }))}
                    error={!!formErrors.endDate}
                    helperText={formErrors.endDate}
                    InputLabelProps={{ shrink: true }}
                  />
                </Grid>
              </Grid>
            </Box>

            <Box sx={{ mt: 3 }}>
              <Grid container spacing={2}>
                <Grid size={{ xs: 6 }}>
                  <TextField
                    fullWidth
                    label="Initial Capital ($)"
                    type="number"
                    value={formData.initialCapital}
                    onChange={(e) => setFormData(prev => ({ ...prev, initialCapital: parseFloat(e.target.value) || 0 }))}
                    error={!!formErrors.initialCapital}
                    helperText={formErrors.initialCapital}
                    inputProps={{ min: 1, step: 1000 }}
                  />
                </Grid>
                <Grid size={{ xs: 6 }}>
                  <TextField
                    fullWidth
                    label="Margin Requirement"
                    type="number"
                    value={formData.marginRequirement}
                    onChange={(e) => setFormData(prev => ({ ...prev, marginRequirement: parseFloat(e.target.value) || 0 }))}
                    error={!!formErrors.marginRequirement}
                    helperText={formErrors.marginRequirement || 'Ratio between 0 and 1 (e.g., 0.5 for 50%)'}
                    inputProps={{ min: 0, max: 1, step: 0.1 }}
                  />
                </Grid>
              </Grid>
            </Box>

            <Box sx={{ mt: 3 }}>
              <Button
                variant="contained"
                size="large"
                fullWidth
                startIcon={backtestState.isRunning ? <Stop /> : <PlayArrow />}
                onClick={backtestState.isRunning ? handleCancelBacktest : handleStartBacktest}
                disabled={backtestState.isRunning ? false : !validateForm()}
                color={backtestState.isRunning ? "error" : "primary"}
              >
                {backtestState.isRunning ? 'Cancel Backtest' : 'Start Backtest'}
              </Button>
            </Box>
          </Paper>
        </Grid>

        {/* Results Panel */}
        <Grid size={{ xs: 12, md: 6 }}>
          <Paper elevation={3} sx={{ p: 3, minHeight: 400 }}>
            <Typography variant="h5" gutterBottom>
              Backtest Results
            </Typography>

            {backtestState.error && (
              <Alert severity="error" sx={{ mb: 2 }}>
                {backtestState.error}
              </Alert>
            )}

            {backtestState.isRunning && (
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <CircularProgress size={20} sx={{ mr: 2 }} />
                <Typography>Running backtest...</Typography>
              </Box>
            )}

            {backtestState.progress.length > 0 && (
              <Box sx={{ mb: 2 }}>
                <Typography variant="h6" gutterBottom>Progress:</Typography>
                {backtestState.progress.slice(-3).map((progress, index) => (
                  <Typography key={index} variant="body2" color="text.secondary">
                    {progress.message}
                  </Typography>
                ))}
              </Box>
            )}

            {backtestState.result && (
              <Box>
                <Typography variant="h6" gutterBottom>Performance Summary:</Typography>
                <Card variant="outlined" sx={{ mb: 2 }}>
                  <CardContent>
                    <Grid container spacing={2}>
                      {backtestState.result.results.totalReturn !== undefined && (
                        <Grid size={{ xs: 6 }}>
                          <Typography variant="body2" color="text.secondary">Total Return</Typography>
                          <Typography variant="h6" color={backtestState.result.results.totalReturn >= 0 ? 'success.main' : 'error.main'}>
                            {backtestState.result.results.totalReturn.toFixed(2)}%
                          </Typography>
                        </Grid>
                      )}
                      {backtestState.result.results.sharpeRatio !== undefined && (
                        <Grid size={{ xs: 6 }}>
                          <Typography variant="body2" color="text.secondary">Sharpe Ratio</Typography>
                          <Typography variant="h6">
                            {backtestState.result.results.sharpeRatio.toFixed(3)}
                          </Typography>
                        </Grid>
                      )}
                      {backtestState.result.results.maxDrawdown !== undefined && (
                        <Grid size={{ xs: 6 }}>
                          <Typography variant="body2" color="text.secondary">Max Drawdown</Typography>
                          <Typography variant="h6" color="error.main">
                            {(backtestState.result.results.maxDrawdown * 100).toFixed(2)}%
                          </Typography>
                        </Grid>
                      )}
                      {backtestState.result.results.finalPortfolioValue !== undefined && (
                        <Grid size={{ xs: 6 }}>
                          <Typography variant="body2" color="text.secondary">Final Value</Typography>
                          <Typography variant="h6">
                            ${backtestState.result.results.finalPortfolioValue.toLocaleString()}
                          </Typography>
                        </Grid>
                      )}
                    </Grid>
                  </CardContent>
                </Card>

                <Divider sx={{ my: 2 }} />

                <Typography variant="h6" gutterBottom>Backtest Summary:</Typography>
                <Typography variant="body2">
                  <strong>Period:</strong> {backtestState.result.backtestSummary.startDate} to {backtestState.result.backtestSummary.endDate}
                </Typography>
                <Typography variant="body2">
                  <strong>Initial Capital:</strong> ${backtestState.result.backtestSummary.initialCapital.toLocaleString()}
                </Typography>
                <Typography variant="body2">
                  <strong>Tickers:</strong> {backtestState.result.backtestSummary.tickers.join(', ')}
                </Typography>
                <Typography variant="body2">
                  <strong>Model:</strong> {backtestState.result.backtestSummary.modelUsed}
                </Typography>
              </Box>
            )}

            {!backtestState.isRunning && !backtestState.result && !backtestState.error && (
              <Typography color="text.secondary" sx={{ textAlign: 'center', mt: 4 }}>
                Configure your backtest parameters and click "Start Backtest" to begin.
              </Typography>
            )}
          </Paper>
        </Grid>
      </Grid>
    </Container>
  );
};

export default BacktestPage;
